# N8N Deployment trên Heroku

Dự án này chứa các file cần thiết để triển khai n8n (workflow automation tool) lên Heroku sử dụng Docker.

## Cấu trúc file

- `Dockerfile`: <PERSON><PERSON><PERSON> nghĩa container Docker cho n8n
- `start.sh`: Script khởi động xử lý cấu hình Heroku
- `heroku.yml`: Cấu hình deployment Heroku
- `.dockerignore`: Loại trừ file không cần thiết khi build Docker

## Hướng dẫn triển khai

### 1. Chuẩn bị

```bash
# Cài đặt Heroku CLI nếu chưa có
# https://devcenter.heroku.com/articles/heroku-cli

# Đăng nhập Heroku
heroku login
```

### 2. Tạo ứng dụng Heroku

```bash
# Tạo app mới
heroku create your-n8n-app-name

# Thiết lập stack container
heroku stack:set container -a your-n8n-app-name
```

### 3. <PERSON><PERSON>u hình biến môi trường

```bash
# Thiết lập tên app để tự động cấu hình webhook URL
heroku config:set HEROKU_APP_NAME=your-n8n-app-name -a your-n8n-app-name

# Các biến môi trường tùy chọn khác
heroku config:set N8N_BASIC_AUTH_ACTIVE=true -a your-n8n-app-name
heroku config:set N8N_BASIC_AUTH_USER=admin -a your-n8n-app-name
heroku config:set N8N_BASIC_AUTH_PASSWORD=your-secure-password -a your-n8n-app-name

# Cấu hình database (tùy chọn - mặc định sử dụng SQLite)
# heroku addons:create heroku-postgresql:hobby-dev -a your-n8n-app-name
# heroku config:set DB_TYPE=postgresdb -a your-n8n-app-name
```

### 4. Deploy

```bash
# Thêm remote Heroku
heroku git:remote -a your-n8n-app-name

# Commit và push code
git add .
git commit -m "Initial n8n deployment"
git push heroku master
```

### 5. Truy cập ứng dụng

Sau khi deploy thành công, bạn có thể truy cập n8n tại:
```
https://your-n8n-app-name.herokuapp.com
```

## Lưu ý quan trọng

1. **Dữ liệu**: Heroku có filesystem tạm thời, dữ liệu sẽ bị mất khi dyno restart. Khuyến nghị sử dụng PostgreSQL addon để lưu trữ dữ liệu.

2. **Bảo mật**: Luôn thiết lập basic authentication hoặc các biện pháp bảo mật khác.

3. **Webhook**: URL webhook sẽ được tự động cấu hình dựa trên tên app Heroku.

4. **Tài nguyên**: N8N có thể tiêu tốn nhiều memory, cân nhắc nâng cấp dyno type nếu cần.

## Biến môi trường hữu ích

- `N8N_BASIC_AUTH_ACTIVE`: Bật/tắt basic authentication
- `N8N_BASIC_AUTH_USER`: Username cho basic auth
- `N8N_BASIC_AUTH_PASSWORD`: Password cho basic auth
- `DB_TYPE`: Loại database (sqlite, postgresdb, mysqldb)
- `DB_POSTGRESDB_HOST`: PostgreSQL host
- `DB_POSTGRESDB_DATABASE`: PostgreSQL database name
- `DB_POSTGRESDB_USER`: PostgreSQL username
- `DB_POSTGRESDB_PASSWORD`: PostgreSQL password

## Troubleshooting

Xem logs để debug:
```bash
heroku logs --tail -a your-n8n-app-name
```
