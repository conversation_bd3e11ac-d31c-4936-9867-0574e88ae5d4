# Sử dụng Node.js 18 Alpine image làm base
FROM node:18-alpine

# Thiết lập thư mục làm việc
WORKDIR /app

# Cài đặt các dependencies cần thiết cho n8n
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    git \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Tạo user non-root để chạy ứng dụng
RUN addgroup -g 1000 n8n && \
    adduser -u 1000 -G n8n -s /bin/sh -D n8n

# Cài đặt n8n globally
RUN npm install -g n8n

# Tạo thư mục cho n8n data
RUN mkdir -p /home/<USER>/.n8n && \
    chown -R n8n:n8n /home/<USER>

# Chuyển sang user n8n
USER n8n

# Thiết lập biến môi trường
ENV N8N_HOST=0.0.0.0
ENV N8N_PORT=5678
ENV N8N_PROTOCOL=http
ENV NODE_ENV=production
ENV WEBHOOK_URL=https://your-app-name.herokuapp.com/

# Expose port mà Heroku sẽ sử dụng
EXPOSE $PORT

# Tạo script khởi động để sử dụng PORT từ Heroku
COPY --chown=n8n:n8n start.sh /home/<USER>/start.sh
RUN chmod +x /home/<USER>/start.sh

# Khởi động n8n
CMD ["/home/<USER>/start.sh"]
